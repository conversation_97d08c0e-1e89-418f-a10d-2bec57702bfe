'use client';
import React, { useRef, useState, useCallback, useEffect } from 'react';
import { motion, useSpring, useTransform, SpringOptions } from 'framer-motion';
import { cn } from '@/lib/utils';

type CursorSpotlightProps = {
  className?: string;
  size?: number;
  springOptions?: SpringOptions;
};

export function CursorSpotlight({
  className,
  size = 300,
  springOptions = { bounce: 0, damping: 30, stiffness: 200 },
}: CursorSpotlightProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isHovered, setIsHovered] = useState(false);
  const [parentElement, setParentElement] = useState<HTMLElement | null>(null);

  const mouseX = useSpring(0, springOptions);
  const mouseY = useSpring(0, springOptions);

  const spotlightLeft = useTransform(mouseX, (x) => `${x - size / 2}px`);
  const spotlightTop = useTransform(mouseY, (y) => `${y - size / 2}px`);

  useEffect(() => {
    if (containerRef.current) {
      const parent = containerRef.current.parentElement;
      if (parent) {
        parent.style.position = 'relative';
        parent.style.overflow = 'hidden';
        setParentElement(parent);
      }
    }
  }, []);

  const handleMouseMove = useCallback(
    (event: MouseEvent) => {
      if (!parentElement) return;
      const { left, top } = parentElement.getBoundingClientRect();
      const x = event.clientX - left;
      const y = event.clientY - top;
      mouseX.set(x);
      mouseY.set(y);
      // Debug logging
      console.log('Cursor spotlight mouse move:', { x, y, isHovered });
    },
    [mouseX, mouseY, parentElement, isHovered]
  );

  useEffect(() => {
    if (!parentElement) return;

    const handleMouseEnter = () => {
      console.log('Cursor spotlight: mouse enter');
      setIsHovered(true);
    };

    const handleMouseLeave = () => {
      console.log('Cursor spotlight: mouse leave');
      setIsHovered(false);
    };

    parentElement.addEventListener('mousemove', handleMouseMove);
    parentElement.addEventListener('mouseenter', handleMouseEnter);
    parentElement.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      parentElement.removeEventListener('mousemove', handleMouseMove);
      parentElement.removeEventListener('mouseenter', handleMouseEnter);
      parentElement.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [parentElement, handleMouseMove]);

  return (
    <motion.div
      ref={containerRef}
      className={cn(
        'pointer-events-none absolute rounded-full transition-opacity duration-300 ease-out',
        'bg-[radial-gradient(circle_at_center,var(--tw-gradient-stops),transparent_50%)]',
        'from-yellow-400/50 via-orange-500/30 to-red-500/20',
        'blur-2xl',
        // Temporarily always visible for debugging
        'opacity-100',
        // isHovered ? 'opacity-100' : 'opacity-0',
        className
      )}
      style={{
        width: size,
        height: size,
        left: spotlightLeft,
        top: spotlightTop,
        willChange: 'transform, opacity',
        transform: 'translateZ(0)',
        zIndex: 2,
      }}
    />
  );
}
